import re
from typing import List, Tuple
from collections import Counter
import math


class CVMatcher:
    def __init__(self):
        # Common stop words to ignore
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'between', 'among', 'is', 'are', 'was', 'were', 'be', 'been',
            'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
        }

    def preprocess_text(self, text: str) -> List[str]:
        """Preprocess text by cleaning and tokenizing"""
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters and keep only alphanumeric and spaces
        text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
        
        # Split into words
        words = text.split()
        
        # Remove stop words and short words
        words = [word for word in words if word not in self.stop_words and len(word) > 2]
        
        return words

    def calculate_tf_idf_similarity(self, job_description: str, cv_content: str) -> float:
        """Calculate TF-IDF based similarity with multilingual improvements"""
        # Preprocess texts
        job_words = self.preprocess_text(job_description)
        cv_words = self.preprocess_text(cv_content)

        # Add synonym expansion for better matching
        synonyms = {
            'developer': ['entwickler', 'programmer', 'programmierer'],
            'experience': ['erfahrung', 'expertise'],
            'software': ['software', 'anwendung'],
            'project': ['projekt', 'vorhaben'],
            'team': ['team', 'gruppe'],
            'management': ['management', 'führung', 'leitung'],
            'database': ['datenbank', 'db'],
            'web': ['web', 'internet', 'online'],
            'programming': ['programmierung', 'entwicklung'],
            'skills': ['fähigkeiten', 'kenntnisse']
        }

        # Expand vocabulary with synonyms
        enhanced_job_words = job_words.copy()
        enhanced_cv_words = cv_words.copy()

        for word in job_words:
            if word in synonyms:
                enhanced_job_words.extend(synonyms[word])

        for word in cv_words:
            if word in synonyms:
                enhanced_cv_words.extend(synonyms[word])

        # Create vocabulary from enhanced word lists
        all_words = set(enhanced_job_words + enhanced_cv_words)

        if not all_words:
            return 0.1  # Give a small base score

        # Calculate term frequencies
        job_tf = Counter(enhanced_job_words)
        cv_tf = Counter(enhanced_cv_words)

        # Calculate similarity using cosine similarity
        dot_product = 0
        job_magnitude = 0
        cv_magnitude = 0

        for word in all_words:
            job_freq = job_tf.get(word, 0)
            cv_freq = cv_tf.get(word, 0)

            dot_product += job_freq * cv_freq
            job_magnitude += job_freq ** 2
            cv_magnitude += cv_freq ** 2

        if job_magnitude == 0 or cv_magnitude == 0:
            return 0.1  # Give a small base score instead of 0

        similarity = dot_product / (math.sqrt(job_magnitude) * math.sqrt(cv_magnitude))

        # Scale the similarity to make it more meaningful
        scaled_similarity = similarity * 3.0  # Boost the score

        return min(1.0, scaled_similarity)  # Cap at 1.0

    def calculate_keyword_match(self, job_description: str, cv_content: str) -> float:
        """Calculate keyword-based matching score with improved algorithm"""
        job_words = set(self.preprocess_text(job_description))
        cv_words = set(self.preprocess_text(cv_content))

        if not job_words:
            return 0.0

        # Calculate intersection
        intersection = job_words.intersection(cv_words)

        # Use a more favorable scoring method than Jaccard
        # Focus on how many job requirements are met rather than overall similarity
        job_coverage = len(intersection) / len(job_words) if job_words else 0

        # Add bonus for important keywords
        important_keywords = {
            'developer', 'engineer', 'programmer', 'analyst', 'manager',
            'entwickler', 'ingenieur', 'programmierer', 'analyst', 'manager',
            'experience', 'erfahrung', 'skills', 'fähigkeiten',
            'project', 'projekt', 'team', 'software'
        }

        important_matches = len(intersection.intersection(important_keywords))
        importance_bonus = important_matches * 0.1  # 10% bonus per important keyword

        # Combine coverage with importance bonus
        final_score = job_coverage + importance_bonus

        return min(1.0, final_score)  # Cap at 1.0

    def calculate_skill_match(self, job_description: str, cv_content: str) -> float:
        """Calculate skill-specific matching score with precise requirement matching"""

        # Define skill mappings with exact matches and synonyms (case-insensitive)
        skill_mappings = {
            # Programming Languages (with German context)
            'java': ['java', 'java8', 'java11', 'java17', 'openjdk', 'mit java', 'java und', 'kenntnisse in java'],
            'python': ['python', 'python3', 'django', 'flask', 'fastapi'],
            'javascript': ['javascript', 'js', 'typescript', 'node.js', 'nodejs'],
            'c++': ['c++', 'cpp', 'c plus plus'],
            'c#': ['c#', 'csharp', 'c sharp', '.net'],

            # Frameworks (with German context)
            'spring': ['spring', 'spring boot', 'springboot', 'spring framework', 'mit spring boot', 'java und spring boot'],
            'react': ['react', 'reactjs', 'react.js'],
            'angular': ['angular', 'angularjs'],
            'vue': ['vue', 'vue.js', 'vuejs'],

            # Databases (with German context)
            'postgresql': ['postgresql', 'postgres', 'psql', 'postgresql-datenbanken', 'mit postgresql', 'wie postgresql'],
            'mysql': ['mysql', 'mariadb'],
            'mongodb': ['mongodb', 'mongo'],
            'oracle': ['oracle', 'oracle db'],
            'sql': ['sql', 'structured query language'],

            # DevOps & Tools (with German context)
            'git': ['git', 'github', 'gitlab', 'bitbucket', 'nutzung von git', 'erfahrung mit git'],
            'jenkins': ['jenkins', 'ci/cd', 'continuous integration', 'git und jenkins'],
            'docker': ['docker', 'containerization', 'containers'],
            'kubernetes': ['kubernetes', 'k8s', 'container orchestration'],

            # Methodologies (with German context)
            'scrum': ['scrum', 'agile', 'sprint', 'scrum-team', 'agilen scrum-team', 'agilen methoden', 'scrum)'],
            'kanban': ['kanban', 'lean'],
            'rest': ['rest', 'restful', 'rest api', 'api', 'rest-services', 'entwicklung von rest-services', 'rest-apis'],

            # German technical terms
            'softwareentwicklung': ['software development', 'software engineering', 'softwareentwicklerin', 'softwareentwickler'],
            'webentwicklung': ['web development', 'web programming', 'webanwendungen', 'webanwendung'],
            'datenbank': ['database', 'db', 'datenbanken', 'relationalen datenbanken'],
            'programmierung': ['programming', 'coding'],
            'berufserfahrung': ['experience', 'work experience', 'professional experience'],
        }

        # Education keywords (comprehensive with German context)
        education_keywords = {
            'computer science': [
                'computer science', 'informatik', 'cs degree', 'bachelor informatik', 'master informatik',
                'b.sc. informatik', 'm.sc. informatik', 'bachelor of science informatik',
                'master of science informatik', 'diplom informatik', 'studium der informatik',
                'abgeschlossenes studium der informatik', 'informatikstudium'
            ],
            'software engineering': ['software engineering', 'software entwicklung'],
            'information technology': ['information technology', 'it', 'informationstechnik'],
            'engineering': ['engineering', 'ingenieur', 'ingenieurwesen'],
            'qualification': ['qualifikation', 'vergleichbare qualifikation', 'qualification', 'degree'],
        }

        # Experience patterns (enhanced for German CVs)
        experience_patterns = [
            r'(\d+)\+?\s*(?:years?|jahre?)\s*(?:of\s*)?(?:experience|erfahrung)',
            r'(\d+)-(\d+)\s*(?:years?|jahre?)',
            r'(?:experience|erfahrung)\s*:?\s*(\d+)\+?\s*(?:years?|jahre?)',
            r'mindestens\s+(\d+)\s+jahre?\s+berufserfahrung',  # German: "mindestens X Jahre Berufserfahrung"
            r'(\d+)\s+jahre?\s+berufserfahrung',  # German: "X Jahre Berufserfahrung"
            r'(\d{4})-(\d{4})',  # Year ranges like 2018-2020
            r'(\d{4})-heute',    # German "until today"
            r'(\d{4})-present',  # English "until present"
        ]

        job_lower = job_description.lower()
        cv_lower = cv_content.lower()

        # Extract required skills from job description
        required_skills = []
        for skill, synonyms in skill_mappings.items():
            for synonym in synonyms:
                if synonym in job_lower:
                    required_skills.append(skill)
                    break

        # Extract candidate skills from CV
        candidate_skills = []
        for skill, synonyms in skill_mappings.items():
            for synonym in synonyms:
                if synonym in cv_lower:
                    candidate_skills.append(skill)
                    break

        # Check education requirements
        education_match = 0
        for edu_type, keywords in education_keywords.items():
            if any(keyword in job_lower for keyword in keywords):
                if any(keyword in cv_lower for keyword in keywords):
                    education_match = 1
                    break

        # Check experience requirements
        import re
        from datetime import datetime
        experience_match = 0
        required_years = 0
        candidate_years = 0

        # Extract required years from job description
        for pattern in experience_patterns[:3]:  # Only use explicit year patterns for requirements
            matches = re.findall(pattern, job_lower)
            if matches:
                if isinstance(matches[0], tuple):
                    required_years = int(matches[0][0])
                else:
                    required_years = int(matches[0])
                break

        # Extract candidate years from CV - calculate from year ranges
        current_year = datetime.now().year
        total_experience = 0

        # Look for year ranges in CV
        year_ranges = re.findall(r'(\d{4})-(\d{4})', cv_lower)
        for start_year, end_year in year_ranges:
            total_experience += int(end_year) - int(start_year)

        # Look for "until today" patterns
        current_jobs = re.findall(r'(\d{4})-heute', cv_lower)
        for start_year in current_jobs:
            total_experience += current_year - int(start_year[0] if isinstance(start_year, tuple) else start_year)

        candidate_years = total_experience

        # Also check for explicit experience mentions
        for pattern in experience_patterns[:3]:
            matches = re.findall(pattern, cv_lower)
            if matches:
                if isinstance(matches[0], tuple):
                    explicit_years = int(matches[0][1])
                else:
                    explicit_years = int(matches[0])
                candidate_years = max(candidate_years, explicit_years)
                break

        if candidate_years >= required_years and required_years > 0:
            experience_match = 1
        elif required_years == 0:
            experience_match = 0.5  # No specific requirement
        elif candidate_years > 0:
            # Partial credit for some experience
            experience_match = min(candidate_years / required_years, 1.0)

        # Calculate skill match score
        if not required_skills:
            skill_score = 0.5  # No specific skills mentioned
        else:
            matched_skills = set(required_skills).intersection(set(candidate_skills))
            skill_score = len(matched_skills) / len(required_skills)

        # Weighted final score
        # Skills: 60%, Education: 20%, Experience: 20%
        final_score = (skill_score * 0.6) + (education_match * 0.2) + (experience_match * 0.2)

        return min(1.0, final_score)

    def calculate_match_score(self, cv_path: str, job_description: str) -> float:
        """Calculate overall match score between CV and job description"""
        try:
            # Extract text from CV
            from cv_extractor import CVDataExtractor
            extractor = CVDataExtractor()
            cv_content = extractor.extract_text_from_file(cv_path)
            
            if not cv_content:
                return 0.0
            
            # Calculate different similarity metrics
            tf_idf_score = self.calculate_tf_idf_similarity(job_description, cv_content)
            keyword_score = self.calculate_keyword_match(job_description, cv_content)
            skill_score = self.calculate_skill_match(job_description, cv_content)
            
            # Improved weighted combination of scores
            # Skills: 50%, Keywords: 30%, TF-IDF: 20% (prioritize skills and keywords)
            overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
            
            # Convert to percentage
            return overall_score * 100
            
        except Exception as e:
            print(f"Error calculating match score: {e}")
            return 0.0

    def match(self, job_description: str, cv_contents: List[str]) -> List[Tuple[float, str]]:
        """Match multiple CVs against a job description and return sorted results"""
        results = []
        
        for i, cv_content in enumerate(cv_contents):
            # Calculate different similarity metrics
            tf_idf_score = self.calculate_tf_idf_similarity(job_description, cv_content)
            keyword_score = self.calculate_keyword_match(job_description, cv_content)
            skill_score = self.calculate_skill_match(job_description, cv_content)
            
            # Improved weighted combination of scores
            overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
            
            results.append((overall_score, cv_content))
        
        # Sort by score in descending order
        results.sort(key=lambda x: x[0], reverse=True)
        
        return results

    def get_match_explanation(self, job_description: str, cv_content: str) -> dict:
        """Get detailed explanation of match score"""
        tf_idf_score = self.calculate_tf_idf_similarity(job_description, cv_content)
        keyword_score = self.calculate_keyword_match(job_description, cv_content)
        skill_score = self.calculate_skill_match(job_description, cv_content)
        
        overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
        
        # Find common keywords
        job_words = set(self.preprocess_text(job_description))
        cv_words = set(self.preprocess_text(cv_content))
        common_keywords = job_words.intersection(cv_words)
        
        return {
            'overall_score': overall_score * 100,
            'tf_idf_score': tf_idf_score * 100,
            'keyword_score': keyword_score * 100,
            'skill_score': skill_score * 100,
            'common_keywords': list(common_keywords)[:10],  # Top 10 common keywords
            'explanation': f"Overall match: {overall_score*100:.1f}% "
                          f"(Content similarity: {tf_idf_score*100:.1f}%, "
                          f"Keyword match: {keyword_score*100:.1f}%, "
                          f"Skill match: {skill_score*100:.1f}%)"
        }
